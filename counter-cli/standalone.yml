services:
  proof-server:
    container_name: 'counter-proof-server'
    image: "midnightnetwork/proof-server:4.0.0"
    ports:
      - "6300:6300"
    environment:
      RUST_BACKTRACE: "full"
  indexer:
    container_name: 'counter-indexer'
    image: 'midnightntwrk/indexer-standalone:2.1.1'
    ports:
      - '8088:8088'
    command: ['-Dlogback.configurationFile=logback-json-file.xml']
    environment:
      RUST_LOG: "indexer=info,chain_indexer=info,indexer_api=info,wallet_indexer=info,indexer_common=info,fastrace_opentelemetry=off,info"
      # Random 32-byte hex-encoded secret used to make the standalone indexer run.
      # Only needed to satisfy the config schema – not meant for secure use.
      APP__INFRA__SECRET: "303132333435363738393031323334353637383930313233343536373839303132"
      APP__INFRA__NODE__URL: "ws://node:9944"
    depends_on:
      node:
        condition: service_started
  node:
    image: 'midnightnetwork/midnight-node:0.12.0'
    container_name: 'counter-node'
    ports:
      - "9944:9944"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9944/health" ]
      interval: 2s
      timeout: 5s
      retries: 5
      start_period: 40s
    environment:
      CFG_PRESET: "dev"
