{"name": "@meshsdk/counter-cli", "version": "0.1.0", "license": "Apache-2.0", "private": true, "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.js", "default": "./dist/index.js"}}, "scripts": {"testnet-remote-ps": "node --experimental-specifier-resolution=node --loader ts-node/esm src/testnet-remote-start-proof-server.ts", "standalone": "node --experimental-specifier-resolution=node --loader ts-node/esm src/standalone.ts", "test-counter-api-standalone": "DEBUG='testcontainers' vitest run counter.api.test.ts", "test-counter-api-testnet": "RUN_ENV_TESTS=true TEST_ENV=testnet TEST_WALLET_SEED=1dec0dd58fbe4d3206ef960aebff95a77e09dffbd19f3e9439d23fe6de4fcdd1 SYNC_CACHE=./src/test vitest run counter.api.test.ts", "prepare-standalone": "DEBUG='testcontainers' vitest run prepare-standalone.test.ts", "build": "rm -rf dist && tsc --project tsconfig.build.json", "lint": "eslint src", "typecheck": "tsc -p tsconfig.json --noEmit", "run-proof-server-testnet": "docker compose -f proof-server-testnet.yml up"}, "dependencies": {"@bitcoin-js/tiny-secp256k1-asmjs": "^2.2.4", "@meshsdk/counter-contract": "*", "@midnight-ntwrk/wallet-sdk-hd": "^2.0.0", "bip32": "^5.0.0-rc.0", "bip39": "^3.1.0", "effect": "^3.16.16"}}