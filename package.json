{"name": "@meshsdk/starter-template", "version": "0.1.0", "private": true, "type": "module", "workspaces": ["counter-cli", "counter-contract", "bulletin-board", "bulletin-board-cli", "frontend-vite-react"], "scripts": {"compact": "turbo run compact", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "dev:proof-server": "cd counter-cli && npm run run-proof-server-testnet", "dev:undeployed-instances": "cd counter-cli && npm run prepare-standalone", "dev:frontend": "cd frontend-vite-react && npm run dev", "start-app-testnet": "concurrently \"npm run dev:proof-server\" \"npm run dev:frontend\"", "start-app-undeployed": "npm run dev:frontend"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.9.2", "devDependencies": {"@eslint/js": "^9.28.0", "@types/node": "^22.15.29", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "concurrently": "^9.2.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "testcontainers": "^11.0.0", "ts-node": "^10.9.2", "turbo": "^2.4.4", "typescript": "^5.8.3", "vitest": "^3.2.0"}, "dependencies": {"@meshsdk/midnight-react": "^0.0.6", "@midnight-ntwrk/compact-runtime": "0.8.1", "@midnight-ntwrk/dapp-connector-api": "3.0.0", "@midnight-ntwrk/ledger": "4.0.0", "@midnight-ntwrk/midnight-js-contracts": "2.0.2", "@midnight-ntwrk/midnight-js-fetch-zk-config-provider": "2.0.2", "@midnight-ntwrk/midnight-js-http-client-proof-provider": "2.0.2", "@midnight-ntwrk/midnight-js-indexer-public-data-provider": "2.0.2", "@midnight-ntwrk/midnight-js-level-private-state-provider": "2.0.2", "@midnight-ntwrk/midnight-js-node-zk-config-provider": "2.0.2", "@midnight-ntwrk/midnight-js-types": "2.0.2", "@midnight-ntwrk/wallet": "5.0.0", "@midnight-ntwrk/wallet-api": "5.0.0", "@midnight-ntwrk/zswap": "4.0.0", "pino": "9.7.0", "pino-pretty": "13.0.0", "vite-plugin-node-polyfills": "^0.24.0", "ws": "8.18.2"}}