{"name": "@meshsdk/frontend-vite-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run copy-contract-keys && tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "copy-contract-keys": "mkdir -p ./public/midnight/counter/keys && mkdir -p ./public/midnight/counter/zkir && cp -r ../counter-contract/dist/managed/counter/keys/* ./public/midnight/counter/keys && cp -r ../counter-contract/dist/managed/counter/zkir/* ./public/midnight/counter/zkir"}, "dependencies": {"@meshsdk/counter-contract": "*", "@meshsdk/midnight-core": "^0.0.6", "@meshsdk/midnight-react": "^0.0.6", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.517.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "6.17.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.25.0", "@originjs/vite-plugin-commonjs": "1.0.3", "@rollup/plugin-commonjs": "^28.0.1", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.4.1"}}